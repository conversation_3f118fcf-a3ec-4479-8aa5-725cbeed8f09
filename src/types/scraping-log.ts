import { Tables } from '@/integrations/supabase/types';

// Base type from Supabase
export type ScrapingLogRow = Tables<'scraping_logs'>;

// Enhanced interface for frontend use
export interface ScrapingLog {
  id: string;
  startedAt: Date;
  finishedAt: Date | null;
  durationSeconds: number | null;
  status: 'running' | 'success' | 'error' | 'partial';
  source: string;
  totalJobsFound: number;
  jobsImported: number;
  jobsUpdated: number;
  jobsDeactivated: number;
  jobsFailed: number;
  errorMessage: string | null;
  errorDetails: any | null;
  metadata: any | null;
  createdAt: Date;
  updatedAt: Date;
}

// Status display configuration
export interface ScrapingLogStatusConfig {
  label: string;
  color: string;
  bgColor: string;
  icon: string;
}

// Filter options for scraping logs
export interface ScrapingLogFilters {
  status?: 'running' | 'success' | 'error' | 'partial' | '';
  source?: string;
  dateFrom?: Date | null;
  dateTo?: Date | null;
  search?: string;
}

// Pagination for scraping logs
export interface ScrapingLogPagination {
  page: number;
  pageSize: number;
  total: number;
  totalPages: number;
}

// Sort options for scraping logs
export type ScrapingLogSortBy = 
  | 'started_at_desc' 
  | 'started_at_asc' 
  | 'duration_desc' 
  | 'duration_asc'
  | 'status_asc'
  | 'status_desc';

// Export format options
export type ExportFormat = 'csv' | 'json';

// Metrics summary for dashboard
export interface ScrapingLogMetrics {
  totalExecutions: number;
  successfulExecutions: number;
  failedExecutions: number;
  averageDuration: number | null;
  totalJobsProcessed: number;
  lastExecution: Date | null;
}

// Log creation payload
export interface CreateScrapingLogPayload {
  status: 'running' | 'success' | 'error' | 'partial';
  source?: string;
  totalJobsFound?: number;
  jobsImported?: number;
  jobsUpdated?: number;
  jobsDeactivated?: number;
  jobsFailed?: number;
  errorMessage?: string | null;
  errorDetails?: any | null;
  metadata?: any | null;
}

// Log update payload
export interface UpdateScrapingLogPayload {
  finishedAt?: Date;
  status?: 'running' | 'success' | 'error' | 'partial';
  totalJobsFound?: number;
  jobsImported?: number;
  jobsUpdated?: number;
  jobsDeactivated?: number;
  jobsFailed?: number;
  errorMessage?: string | null;
  errorDetails?: any | null;
  metadata?: any | null;
}

// Transform function from Supabase row to frontend interface
export function transformScrapingLogRow(row: ScrapingLogRow): ScrapingLog {
  return {
    id: row.id,
    startedAt: new Date(row.started_at),
    finishedAt: row.finished_at ? new Date(row.finished_at) : null,
    durationSeconds: row.duration_seconds,
    status: row.status,
    source: row.source,
    totalJobsFound: row.total_jobs_found,
    jobsImported: row.jobs_imported,
    jobsUpdated: row.jobs_updated,
    jobsDeactivated: row.jobs_deactivated,
    jobsFailed: row.jobs_failed,
    errorMessage: row.error_message,
    errorDetails: row.error_details,
    metadata: row.metadata,
    createdAt: new Date(row.created_at),
    updatedAt: new Date(row.updated_at),
  };
}

// Status configurations for UI display
export const SCRAPING_LOG_STATUS_CONFIG: Record<ScrapingLog['status'], ScrapingLogStatusConfig> = {
  running: {
    label: 'Em Execução',
    color: 'text-blue-700',
    bgColor: 'bg-blue-50 border-blue-200',
    icon: 'RefreshCw',
  },
  success: {
    label: 'Sucesso',
    color: 'text-green-700',
    bgColor: 'bg-green-50 border-green-200',
    icon: 'CheckCircle',
  },
  error: {
    label: 'Erro',
    color: 'text-red-700',
    bgColor: 'bg-red-50 border-red-200',
    icon: 'XCircle',
  },
  partial: {
    label: 'Parcial',
    color: 'text-yellow-700',
    bgColor: 'bg-yellow-50 border-yellow-200',
    icon: 'AlertTriangle',
  },
};
