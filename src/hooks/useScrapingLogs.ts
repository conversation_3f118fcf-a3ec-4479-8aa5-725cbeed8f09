import { useState, useEffect, useCallback } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { 
  ScrapingLog, 
  ScrapingLogFilters, 
  ScrapingLogPagination, 
  ScrapingLogSortBy,
  ScrapingLogMetrics,
  transformScrapingLogRow,
  CreateScrapingLogPayload,
  UpdateScrapingLogPayload
} from '@/types/scraping-log';
import { useToast } from '@/hooks/use-toast';

interface UseScrapingLogsReturn {
  logs: ScrapingLog[];
  isLoading: boolean;
  error: string | null;
  filters: ScrapingLogFilters;
  pagination: ScrapingLogPagination;
  sortBy: ScrapingLogSortBy;
  metrics: ScrapingLogMetrics | null;
  
  // Actions
  updateFilters: (newFilters: Partial<ScrapingLogFilters>) => void;
  updatePagination: (newPagination: Partial<ScrapingLogPagination>) => void;
  setSortBy: (sortBy: ScrapingLogSortBy) => void;
  clearFilters: () => void;
  refreshLogs: () => Promise<void>;
  changePage: (page: number) => void;
  changePageSize: (pageSize: number) => void;
  deleteLog: (logId: string) => Promise<boolean>;
  deleteOldLogs: (daysOld: number) => Promise<number>;
  exportLogs: (format: 'csv' | 'json') => Promise<void>;
  createLog: (payload: CreateScrapingLogPayload) => Promise<string | null>;
  updateLog: (logId: string, payload: UpdateScrapingLogPayload) => Promise<boolean>;
}

const DEFAULT_FILTERS: ScrapingLogFilters = {
  status: '',
  source: '',
  dateFrom: null,
  dateTo: null,
  search: '',
};

const DEFAULT_PAGINATION: ScrapingLogPagination = {
  page: 1,
  pageSize: 20,
  total: 0,
  totalPages: 0,
};

export function useScrapingLogs(): UseScrapingLogsReturn {
  const { toast } = useToast();
  const [logs, setLogs] = useState<ScrapingLog[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState<ScrapingLogFilters>(DEFAULT_FILTERS);
  const [pagination, setPagination] = useState<ScrapingLogPagination>(DEFAULT_PAGINATION);
  const [sortBy, setSortBy] = useState<ScrapingLogSortBy>('started_at_desc');
  const [metrics, setMetrics] = useState<ScrapingLogMetrics | null>(null);

  // Build query based on filters and sorting
  const buildQuery = useCallback(() => {
    let query = supabase
      .from('scraping_logs')
      .select('*', { count: 'exact' });

    // Apply filters
    if (filters.status) {
      query = query.eq('status', filters.status);
    }

    if (filters.source) {
      query = query.eq('source', filters.source);
    }

    if (filters.dateFrom) {
      query = query.gte('started_at', filters.dateFrom.toISOString());
    }

    if (filters.dateTo) {
      query = query.lte('started_at', filters.dateTo.toISOString());
    }

    if (filters.search) {
      query = query.or(`source.ilike.%${filters.search}%,error_message.ilike.%${filters.search}%`);
    }

    // Apply sorting
    const [field, direction] = sortBy.split('_');
    const ascending = direction === 'asc';
    
    switch (field) {
      case 'started':
        query = query.order('started_at', { ascending });
        break;
      case 'duration':
        query = query.order('duration_seconds', { ascending, nullsFirst: false });
        break;
      case 'status':
        query = query.order('status', { ascending });
        break;
      default:
        query = query.order('started_at', { ascending: false });
    }

    // Apply pagination
    const from = (pagination.page - 1) * pagination.pageSize;
    const to = from + pagination.pageSize - 1;
    query = query.range(from, to);

    return query;
  }, [filters, sortBy, pagination.page, pagination.pageSize]);

  // Fetch logs
  const fetchLogs = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      const query = buildQuery();
      const { data, error: queryError, count } = await query;

      if (queryError) {
        throw queryError;
      }

      const transformedLogs = (data || []).map(transformScrapingLogRow);
      setLogs(transformedLogs);

      // Update pagination with total count
      setPagination(prev => ({
        ...prev,
        total: count || 0,
        totalPages: Math.ceil((count || 0) / prev.pageSize),
      }));

    } catch (err: any) {
      console.error('Error fetching scraping logs:', err);
      setError(err.message || 'Erro ao carregar logs de scraping');
      toast({
        title: "Erro",
        description: "Não foi possível carregar os logs de scraping",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }, [buildQuery, toast]);

  // Fetch metrics
  const fetchMetrics = useCallback(async () => {
    try {
      const { data, error } = await supabase
        .from('scraping_logs')
        .select('status, duration_seconds, jobs_imported, jobs_updated, jobs_deactivated, jobs_failed, started_at')
        .order('started_at', { ascending: false });

      if (error) throw error;

      if (data && data.length > 0) {
        const totalExecutions = data.length;
        const successfulExecutions = data.filter(log => log.status === 'success').length;
        const failedExecutions = data.filter(log => log.status === 'error').length;
        
        const durationsWithValues = data
          .filter(log => log.duration_seconds !== null)
          .map(log => log.duration_seconds!);
        
        const averageDuration = durationsWithValues.length > 0 
          ? durationsWithValues.reduce((sum, duration) => sum + duration, 0) / durationsWithValues.length
          : null;

        const totalJobsProcessed = data.reduce((sum, log) => 
          sum + (log.jobs_imported || 0) + (log.jobs_updated || 0) + (log.jobs_deactivated || 0), 0);

        const lastExecution = new Date(data[0].started_at);

        setMetrics({
          totalExecutions,
          successfulExecutions,
          failedExecutions,
          averageDuration,
          totalJobsProcessed,
          lastExecution,
        });
      } else {
        setMetrics({
          totalExecutions: 0,
          successfulExecutions: 0,
          failedExecutions: 0,
          averageDuration: null,
          totalJobsProcessed: 0,
          lastExecution: null,
        });
      }
    } catch (err: any) {
      console.error('Error fetching metrics:', err);
    }
  }, []);

  // Actions
  const updateFilters = useCallback((newFilters: Partial<ScrapingLogFilters>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
    setPagination(prev => ({ ...prev, page: 1 })); // Reset to first page
  }, []);

  const updatePagination = useCallback((newPagination: Partial<ScrapingLogPagination>) => {
    setPagination(prev => ({ ...prev, ...newPagination }));
  }, []);

  const clearFilters = useCallback(() => {
    setFilters(DEFAULT_FILTERS);
    setPagination(prev => ({ ...prev, page: 1 }));
  }, []);

  const refreshLogs = useCallback(async () => {
    await Promise.all([fetchLogs(), fetchMetrics()]);
  }, [fetchLogs, fetchMetrics]);

  const changePage = useCallback((page: number) => {
    setPagination(prev => ({ ...prev, page }));
  }, []);

  const changePageSize = useCallback((pageSize: number) => {
    setPagination(prev => ({ 
      ...prev, 
      pageSize, 
      page: 1,
      totalPages: Math.ceil(prev.total / pageSize)
    }));
  }, []);

  const deleteLog = useCallback(async (logId: string): Promise<boolean> => {
    try {
      const { error } = await supabase
        .from('scraping_logs')
        .delete()
        .eq('id', logId);

      if (error) throw error;

      toast({
        title: "Sucesso",
        description: "Log removido com sucesso",
      });

      await refreshLogs();
      return true;
    } catch (err: any) {
      console.error('Error deleting log:', err);
      toast({
        title: "Erro",
        description: "Não foi possível remover o log",
        variant: "destructive",
      });
      return false;
    }
  }, [toast, refreshLogs]);

  const deleteOldLogs = useCallback(async (daysOld: number): Promise<number> => {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - daysOld);

      const { data, error } = await supabase
        .from('scraping_logs')
        .delete()
        .lt('created_at', cutoffDate.toISOString())
        .select('id');

      if (error) throw error;

      const deletedCount = data?.length || 0;
      
      toast({
        title: "Sucesso",
        description: `${deletedCount} logs antigos foram removidos`,
      });

      await refreshLogs();
      return deletedCount;
    } catch (err: any) {
      console.error('Error deleting old logs:', err);
      toast({
        title: "Erro",
        description: "Não foi possível remover logs antigos",
        variant: "destructive",
      });
      return 0;
    }
  }, [toast, refreshLogs]);

  const exportLogs = useCallback(async (format: 'csv' | 'json') => {
    try {
      // Fetch all logs without pagination for export
      const { data, error } = await supabase
        .from('scraping_logs')
        .select('*')
        .order('started_at', { ascending: false });

      if (error) throw error;

      const transformedLogs = (data || []).map(transformScrapingLogRow);
      
      if (format === 'json') {
        const jsonData = JSON.stringify(transformedLogs, null, 2);
        const blob = new Blob([jsonData], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `scraping-logs-${new Date().toISOString().split('T')[0]}.json`;
        a.click();
        URL.revokeObjectURL(url);
      } else {
        // CSV format
        const headers = [
          'ID', 'Data Início', 'Data Fim', 'Duração (s)', 'Status', 'Fonte',
          'Total Encontradas', 'Importadas', 'Atualizadas', 'Desativadas', 'Falharam',
          'Mensagem de Erro'
        ];
        
        const csvRows = [
          headers.join(','),
          ...transformedLogs.map(log => [
            log.id,
            log.startedAt.toISOString(),
            log.finishedAt?.toISOString() || '',
            log.durationSeconds || '',
            log.status,
            log.source,
            log.totalJobsFound,
            log.jobsImported,
            log.jobsUpdated,
            log.jobsDeactivated,
            log.jobsFailed,
            `"${log.errorMessage || ''}"`
          ].join(','))
        ];
        
        const csvData = csvRows.join('\n');
        const blob = new Blob([csvData], { type: 'text/csv;charset=utf-8;' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `scraping-logs-${new Date().toISOString().split('T')[0]}.csv`;
        a.click();
        URL.revokeObjectURL(url);
      }

      toast({
        title: "Sucesso",
        description: `Logs exportados em formato ${format.toUpperCase()}`,
      });
    } catch (err: any) {
      console.error('Error exporting logs:', err);
      toast({
        title: "Erro",
        description: "Não foi possível exportar os logs",
        variant: "destructive",
      });
    }
  }, [toast]);

  const createLog = useCallback(async (payload: CreateScrapingLogPayload): Promise<string | null> => {
    try {
      const { data, error } = await supabase
        .from('scraping_logs')
        .insert(payload)
        .select('id')
        .single();

      if (error) throw error;

      return data?.id || null;
    } catch (err: any) {
      console.error('Error creating log:', err);
      return null;
    }
  }, []);

  const updateLog = useCallback(async (logId: string, payload: UpdateScrapingLogPayload): Promise<boolean> => {
    try {
      const { error } = await supabase
        .from('scraping_logs')
        .update(payload)
        .eq('id', logId);

      if (error) throw error;

      return true;
    } catch (err: any) {
      console.error('Error updating log:', err);
      return false;
    }
  }, []);

  // Effects
  useEffect(() => {
    fetchLogs();
  }, [fetchLogs]);

  useEffect(() => {
    fetchMetrics();
  }, [fetchMetrics]);

  return {
    logs,
    isLoading,
    error,
    filters,
    pagination,
    sortBy,
    metrics,
    updateFilters,
    updatePagination,
    setSortBy,
    clearFilters,
    refreshLogs,
    changePage,
    changePageSize,
    deleteLog,
    deleteOldLogs,
    exportLogs,
    createLog,
    updateLog,
  };
}
