import { useState, useEffect } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useToast } from "@/hooks/use-toast";
import { ArrowLeft, Download, Plus, Trash2, Edit, Globe, RefreshCw, Eye, Activity } from "lucide-react";
import { Link } from "react-router-dom";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { supabase } from "@/integrations/supabase/client";
import { useAdminJobFilters } from "@/hooks/useAdminJobFilters";
import { AdminJobFilters } from "@/components/AdminJobFilters";
import { AdminJobPagination } from "@/components/AdminJobPagination";
import { AdminJobCard } from "@/components/AdminJobCard";
import { Job } from "@/types/job";
import { useScrapingLogs } from "@/hooks/useScrapingLogs";
import { ScrapingLogFilters } from "@/components/ScrapingLogFilters";
import { ScrapingLogTable } from "@/components/ScrapingLogTable";
import { ScrapingLogPaginationComponent } from "@/components/ScrapingLogPagination";
import { ScrapingLogMetrics } from "@/components/ScrapingLogMetrics";
import { DeleteOldLogsDialog } from "@/components/DeleteOldLogsDialog";

// Component for viewing job details
const JobViewDialog = ({
  job,
  isOpen,
  onClose,
  onToggleStatus
}: {
  job: Job;
  isOpen: boolean;
  onClose: () => void;
  onToggleStatus?: (jobId: string, isActive: boolean) => void;
}) => {
  const formatDate = (date: Date) => {
    return date.toLocaleDateString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-3xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Detalhes da Vaga</DialogTitle>
        </DialogHeader>
        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label className="text-sm font-medium text-muted-foreground">Título</Label>
              <p className="text-sm">{job.title}</p>
            </div>
            <div>
              <Label className="text-sm font-medium text-muted-foreground">Empresa</Label>
              <p className="text-sm">{job.company}</p>
            </div>
            <div>
              <Label className="text-sm font-medium text-muted-foreground">Localização</Label>
              <p className="text-sm">{job.location}</p>
            </div>
            <div>
              <Label className="text-sm font-medium text-muted-foreground">Categoria</Label>
              <p className="text-sm">{job.category}</p>
            </div>
            <div>
              <Label className="text-sm font-medium text-muted-foreground">Data de Criação</Label>
              <p className="text-sm">{formatDate(job.postedDate)}</p>
            </div>
            <div>
              <Label className="text-sm font-medium text-muted-foreground">Status</Label>
              <p className="text-sm">
                {job.isActive === false ? (
                  <span className="text-red-600">Inativa</span>
                ) : (
                  <span className="text-green-600">Ativa</span>
                )}
              </p>
            </div>
            {job.isActive === false && job.deactivatedAt && (
              <div>
                <Label className="text-sm font-medium text-muted-foreground">Data de Desativação</Label>
                <p className="text-sm text-orange-600">
                  {formatDate(job.deactivatedAt)}
                </p>
              </div>
            )}
            {job.source === 'hardfranca' && (
              <div>
                <Label className="text-sm font-medium text-muted-foreground">ID Externo</Label>
                <p className="text-sm">{job.id}</p>
              </div>
            )}
          </div>
          <div>
            <Label className="text-sm font-medium text-muted-foreground">Descrição</Label>
            <div className="mt-2 p-3 bg-muted/50 rounded-lg">
              <p className="text-sm whitespace-pre-wrap">{job.description}</p>
            </div>
          </div>

          {/* Action buttons */}
          {onToggleStatus && (
            <div className="flex justify-end pt-4 border-t">
              <Button
                variant="outline"
                onClick={() => onToggleStatus(job.id, !job.isActive)}
                className={`${
                  job.isActive === false
                    ? 'text-green-600 hover:text-green-700 hover:bg-green-50 border-green-200'
                    : 'text-orange-600 hover:text-orange-700 hover:bg-orange-50 border-orange-200'
                }`}
              >
                {job.isActive === false ? 'Ativar Vaga' : 'Desativar Vaga'}
              </Button>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};

// Component for editing job
const JobEditDialog = ({ job, isOpen, onClose, onSave }: { 
  job: Job; 
  isOpen: boolean; 
  onClose: () => void; 
  onSave: (updatedJob: Partial<Job>) => void;
}) => {
  const [editForm, setEditForm] = useState({
    title: job.title,
    company: job.company,
    location: job.location,
    category: job.category,
    description: job.description
  });

  const handleSave = () => {
    onSave(editForm);
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Editar Vaga</DialogTitle>
        </DialogHeader>
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="edit-title">Título da Vaga</Label>
              <Input
                id="edit-title"
                value={editForm.title}
                onChange={(e) => setEditForm(prev => ({ ...prev, title: e.target.value }))}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-company">Empresa</Label>
              <Input
                id="edit-company"
                value={editForm.company}
                onChange={(e) => setEditForm(prev => ({ ...prev, company: e.target.value }))}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-location">Localização</Label>
              <Input
                id="edit-location"
                value={editForm.location}
                onChange={(e) => setEditForm(prev => ({ ...prev, location: e.target.value }))}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-category">Categoria</Label>
              <Input
                id="edit-category"
                value={editForm.category}
                onChange={(e) => setEditForm(prev => ({ ...prev, category: e.target.value }))}
              />
            </div>
          </div>
          <div className="space-y-2">
            <Label htmlFor="edit-description">Descrição</Label>
            <Textarea
              id="edit-description"
              value={editForm.description}
              onChange={(e) => setEditForm(prev => ({ ...prev, description: e.target.value }))}
              className="min-h-[120px]"
            />
          </div>
          <div className="flex justify-end gap-2">
            <Button variant="outline" onClick={onClose}>Cancelar</Button>
            <Button onClick={handleSave}>Salvar Alterações</Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

const AdminPanel = () => {
  const { toast } = useToast();
  const [isScrapingJobs, setIsScrapingJobs] = useState(false);
  const [scrapingResults, setScrapingResults] = useState<{ success: number; failed: number; total: number } | null>(null);

  // Dialog states
  const [viewingJob, setViewingJob] = useState<Job | null>(null);
  const [editingJob, setEditingJob] = useState<Job | null>(null);
  const [showDeleteLogsDialog, setShowDeleteLogsDialog] = useState(false);

  // Use the new admin job filters hook
  const {
    filters,
    pagination,
    sortBy,
    jobs,
    isLoading: isLoadingJobs,
    error: jobsError,
    filterOptions,
    hasActiveFilters,
    updateFilters,
    updatePagination,
    setSortBy,
    clearFilters,
    changePage,
    changePageSize,
    refreshJobs
  } = useAdminJobFilters();

  // Use the scraping logs hook
  const {
    logs,
    isLoading: isLoadingLogs,
    error: logsError,
    filters: logFilters,
    pagination: logPagination,
    sortBy: logSortBy,
    metrics,
    updateFilters: updateLogFilters,
    setSortBy: setLogSortBy,
    clearFilters: clearLogFilters,
    changePage: changeLogPage,
    changePageSize: changeLogPageSize,
    deleteLog,
    deleteOldLogs,
    exportLogs,
    refreshLogs
  } = useScrapingLogs();

  // Show error toast when jobs loading fails
  useEffect(() => {
    if (jobsError) {
      toast({
        title: "Erro ao carregar vagas",
        description: jobsError,
        variant: "destructive",
      });
    }
  }, [jobsError, toast]);
  
  // Form state for manual job creation
  const [jobForm, setJobForm] = useState({
    title: '',
    company: '',
    location: '',
    type: '',
    experience: '',
    description: '',
    requirements: '',
    category: ''
  });

  const handleScrapeJobs = async () => {
    setIsScrapingJobs(true);
    setScrapingResults(null);
    
    try {
      console.log('Starting web scraping...');
      
      // Call the Supabase Edge Function for web scraping
      const { data, error } = await supabase.functions.invoke('scrape-hardfranca');
      
      if (error) {
        console.error('Error calling scrape function:', error);
        throw new Error(error.message || 'Failed to call scraping function');
      }
      
      console.log('Scraping response:', data);
      
      if (data.success) {
        setScrapingResults({
          success: data.jobs_imported || 0,
          failed: data.jobs_failed || 0,
          total: data.total_found || 0
        });

        // Reload jobs to show the new scraped data
        refreshJobs();
        refreshLogs();

        const importedCount = data.jobs_imported || 0;
        const updatedCount = data.jobs_updated || 0;
        const deactivatedCount = data.jobs_deactivated || 0;
        const failedCount = data.jobs_failed || 0;

        let description = `${importedCount} vagas novas importadas`;
        if (updatedCount > 0) {
          description += `, ${updatedCount} atualizadas`;
        }
        if (deactivatedCount > 0) {
          description += `, ${deactivatedCount} desativadas`;
        }

        toast({
          title: "Scraping concluído",
          description: description,
        });

        // Show warning notification if there were failures
        if (failedCount > 0) {
          toast({
            title: "Atenção",
            description: `${failedCount} vagas falharam no processamento. Verifique os logs para mais detalhes.`,
            variant: "destructive",
          });
        }
      } else {
        throw new Error(data.error || 'Scraping failed');
      }
      
    } catch (error: any) {
      console.error('Exception during scraping:', error);
      toast({
        title: "Erro no scraping",
        description: error.message || "Falha ao importar vagas. Tente novamente.",
        variant: "destructive",
      });
    } finally {
      setIsScrapingJobs(false);
    }
  };

  const handleCreateJob = async () => {
    if (!jobForm.title || !jobForm.company || !jobForm.description) {
      toast({
        title: "Campos obrigatórios",
        description: "Preencha pelo menos título, empresa e descrição",
        variant: "destructive",
      });
      return;
    }

    try {
      const { error } = await supabase
        .from('jobs')
        .insert({
          title: jobForm.title,
          company: jobForm.company,
          location: jobForm.location || 'Franca, SP',
          description: jobForm.description,
          category: jobForm.category || 'Geral',
          external_id: `manual-${Date.now()}`,
          is_active: true
        });

      if (error) {
        console.error('Error creating job:', error);
        toast({
          title: "Erro ao criar vaga",
          description: "Não foi possível salvar a vaga no banco de dados",
          variant: "destructive",
        });
        return;
      }

      // Reload jobs to show the new job
      refreshJobs();
      
      setJobForm({
        title: '',
        company: '',
        location: '',
        type: '',
        experience: '',
        description: '',
        requirements: '',
        category: ''
      });

      toast({
        title: "Vaga criada",
        description: "Nova vaga adicionada com sucesso",
      });
    } catch (error) {
      console.error('Exception creating job:', error);
      toast({
        title: "Erro ao criar vaga",
        description: "Falha inesperada ao salvar a vaga",
        variant: "destructive",
      });
    }
  };

  const handleDeleteJob = async (id: string) => {
    try {
      const { error } = await supabase
        .from('jobs')
        .delete()
        .eq('id', id);

      if (error) {
        console.error('Error deleting job:', error);
        toast({
          title: "Erro ao deletar vaga",
          description: "Não foi possível deletar a vaga",
          variant: "destructive",
        });
        return;
      }

      // Reload jobs to reflect the deletion
      refreshJobs();
      
      toast({
        title: "Vaga removida",
        description: "Vaga deletada com sucesso",
      });
    } catch (error) {
      console.error('Exception deleting job:', error);
      toast({
        title: "Erro ao deletar vaga",
        description: "Falha inesperada ao deletar a vaga",
        variant: "destructive",
      });
    }
  };

  const handleEditJob = async (jobId: string, updatedData: Partial<Job>) => {
    try {
      const { error } = await supabase
        .from('jobs')
        .update({
          title: updatedData.title,
          company: updatedData.company,
          location: updatedData.location,
          category: updatedData.category,
          description: updatedData.description
        })
        .eq('id', jobId);

      if (error) {
        console.error('Error updating job:', error);
        toast({
          title: "Erro ao editar vaga",
          description: "Não foi possível salvar as alterações",
          variant: "destructive",
        });
        return;
      }

      // Reload jobs to reflect the changes
      refreshJobs();

      toast({
        title: "Vaga atualizada",
        description: "Alterações salvas com sucesso",
      });
    } catch (error) {
      console.error('Exception updating job:', error);
      toast({
        title: "Erro ao editar vaga",
        description: "Falha inesperada ao salvar as alterações",
        variant: "destructive",
      });
    }
  };

  const handleToggleJobStatus = async (jobId: string, isActive: boolean) => {
    try {
      // Update is_active - the database trigger will handle deactivated_at automatically
      const { error } = await supabase
        .from('jobs')
        .update({ is_active: isActive })
        .eq('id', jobId);

      if (error) {
        console.error('Error toggling job status:', error);
        toast({
          title: "Erro ao alterar status",
          description: "Não foi possível alterar o status da vaga",
          variant: "destructive",
        });
        return;
      }

      // Refresh the jobs list
      refreshJobs();

      // If the job being toggled is currently being viewed in the modal, update it immediately
      if (viewingJob && viewingJob.id === jobId) {
        // Fetch updated data from database to get the real deactivated_at timestamp
        setTimeout(async () => {
          const { data: updatedJobData, error: fetchError } = await supabase
            .from('jobs')
            .select('*, deactivated_at')
            .eq('id', jobId)
            .single();

          if (!fetchError && updatedJobData) {
            // Update with the actual database values including real deactivated_at
            const confirmedJob: Job = {
              ...viewingJob,
              isActive: updatedJobData.is_active,
              deactivatedAt: updatedJobData.deactivated_at ? new Date(updatedJobData.deactivated_at) : null
            };
            setViewingJob(confirmedJob);
          }
        }, 500);
      }

      toast({
        title: "Status alterado",
        description: `Vaga ${isActive ? 'ativada' : 'desativada'} com sucesso`,
      });
    } catch (error) {
      console.error('Exception toggling job status:', error);
      toast({
        title: "Erro ao alterar status",
        description: "Falha inesperada ao alterar o status",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-accent/5">
      <header className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Link to="/" className="flex items-center gap-2 text-muted-foreground hover:text-foreground transition-colors">
                <ArrowLeft className="h-4 w-4" />
                Voltar ao Portal
              </Link>
              <div className="h-6 w-px bg-border" />
              <h1 className="text-2xl font-bold">Painel Administrativo</h1>
            </div>
            <Badge variant="secondary">Admin</Badge>
          </div>
        </div>
      </header>

      <main className="container mx-auto px-4 py-8">
        <Tabs defaultValue="scraping" className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="scraping">Web Scraping</TabsTrigger>
            <TabsTrigger value="logs">Logs de Execução</TabsTrigger>
            <TabsTrigger value="create">Criar Vaga</TabsTrigger>
            <TabsTrigger value="manage">Gerenciar Vagas</TabsTrigger>
          </TabsList>

          <TabsContent value="scraping" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Globe className="h-5 w-5" />
                  Web Scraping - HardFranca
                </CardTitle>
                <CardDescription>
                  Importe vagas automaticamente do site https://hardfranca.com.br/anuncios.php
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center gap-4">
                  <Button 
                    onClick={handleScrapeJobs}
                    disabled={isScrapingJobs}
                    className="bg-primary hover:bg-primary/90"
                  >
                    <Download className="h-4 w-4 mr-2" />
                    {isScrapingJobs ? "Importando..." : "Importar Vagas"}
                  </Button>
                  
                  {scrapingResults && (
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                        {scrapingResults.success} importadas
                      </Badge>
                      {scrapingResults.failed > 0 && (
                        <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">
                          {scrapingResults.failed} falharam
                        </Badge>
                      )}
                    </div>
                  )}
                </div>
                
                <div className="p-4 bg-muted/50 rounded-lg">
                  <p className="text-sm text-muted-foreground">
                    <strong>Como funciona:</strong> O sistema extrai vagas da estrutura HTML do HardFranca, 
                    capturando títulos (classe "titulo-anuncio") e descrições completas. 
                    Duplicatas são automaticamente verificadas antes da importação.
                  </p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="logs" className="space-y-6">
            {/* Metrics Dashboard */}
            <ScrapingLogMetrics
              metrics={metrics}
              isLoading={isLoadingLogs}
            />

            {/* Filters */}
            <ScrapingLogFilters
              filters={logFilters}
              onFiltersChange={updateLogFilters}
              onClearFilters={clearLogFilters}
              sortBy={logSortBy}
              onSortChange={setLogSortBy}
              resultsCount={logPagination.total}
              hasActiveFilters={
                (logFilters.status && logFilters.status !== '') ||
                (logFilters.source && logFilters.source !== '') ||
                logFilters.dateFrom !== null ||
                logFilters.dateTo !== null ||
                (logFilters.search && logFilters.search !== '')
              }
              isLoading={isLoadingLogs}
              onExport={exportLogs}
              onDeleteOldLogs={() => setShowDeleteLogsDialog(true)}
            />

            {/* Logs Table */}
            <ScrapingLogTable
              logs={logs}
              isLoading={isLoadingLogs}
              onDeleteLog={deleteLog}
            />

            {/* Pagination */}
            {logPagination.total > 0 && (
              <ScrapingLogPaginationComponent
                pagination={logPagination}
                onPageChange={changeLogPage}
                onPageSizeChange={changeLogPageSize}
                isLoading={isLoadingLogs}
              />
            )}
          </TabsContent>

          <TabsContent value="create" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Plus className="h-5 w-5" />
                  Criar Nova Vaga
                </CardTitle>
                <CardDescription>
                  Adicione uma nova vaga manualmente ao sistema
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="title">Título da Vaga *</Label>
                    <Input
                      id="title"
                      value={jobForm.title}
                      onChange={(e) => setJobForm(prev => ({ ...prev, title: e.target.value }))}
                      placeholder="Ex: Desenvolvedor Frontend"
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="company">Empresa *</Label>
                    <Input
                      id="company"
                      value={jobForm.company}
                      onChange={(e) => setJobForm(prev => ({ ...prev, company: e.target.value }))}
                      placeholder="Ex: Tech Company Ltda"
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="location">Localização</Label>
                    <Input
                      id="location"
                      value={jobForm.location}
                      onChange={(e) => setJobForm(prev => ({ ...prev, location: e.target.value }))}
                      placeholder="Ex: Franca, SP"
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="category">Categoria</Label>
                    <Input
                      id="category"
                      value={jobForm.category}
                      onChange={(e) => setJobForm(prev => ({ ...prev, category: e.target.value }))}
                      placeholder="Ex: Tecnologia"
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label>Tipo de Contrato</Label>
                    <Select value={jobForm.type} onValueChange={(value) => setJobForm(prev => ({ ...prev, type: value }))}>
                      <SelectTrigger>
                        <SelectValue placeholder="Selecione o tipo" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="full-time">Tempo Integral</SelectItem>
                        <SelectItem value="part-time">Meio Período</SelectItem>
                        <SelectItem value="contract">Contrato</SelectItem>
                        <SelectItem value="freelance">Freelance</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div className="space-y-2">
                    <Label>Nível de Experiência</Label>
                    <Select value={jobForm.experience} onValueChange={(value) => setJobForm(prev => ({ ...prev, experience: value }))}>
                      <SelectTrigger>
                        <SelectValue placeholder="Selecione o nível" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="entry">Iniciante</SelectItem>
                        <SelectItem value="mid">Pleno</SelectItem>
                        <SelectItem value="senior">Senior</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="description">Descrição da Vaga *</Label>
                  <Textarea
                    id="description"
                    value={jobForm.description}
                    onChange={(e) => setJobForm(prev => ({ ...prev, description: e.target.value }))}
                    placeholder="Descreva as responsabilidades e detalhes da vaga..."
                    className="min-h-[100px]"
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="requirements">Requisitos</Label>
                  <Textarea
                    id="requirements"
                    value={jobForm.requirements}
                    onChange={(e) => setJobForm(prev => ({ ...prev, requirements: e.target.value }))}
                    placeholder="Liste os requisitos necessários..."
                    className="min-h-[80px]"
                  />
                </div>
                
                <Button onClick={handleCreateJob} className="w-full">
                  <Plus className="h-4 w-4 mr-2" />
                  Criar Vaga
                </Button>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="manage" className="space-y-6">
            {/* Filters */}
            <AdminJobFilters
              filters={filters}
              onFiltersChange={updateFilters}
              onClearFilters={clearFilters}
              sortBy={sortBy}
              onSortChange={setSortBy}
              resultsCount={pagination.total}
              hasActiveFilters={hasActiveFilters}
              filterOptions={filterOptions}
              isLoading={isLoadingJobs}
            />

            {/* Jobs List */}
            <Card>
              <CardHeader>
                <CardTitle>
                  Vagas Cadastradas ({pagination.total})
                  {isLoadingJobs && (
                    <RefreshCw className="inline-block ml-2 h-4 w-4 animate-spin" />
                  )}
                </CardTitle>
                <CardDescription>
                  Gerencie todas as vagas do sistema com filtros e paginação
                </CardDescription>
              </CardHeader>
              <CardContent>
                {isLoadingJobs ? (
                  <div className="text-center py-8 text-muted-foreground">
                    <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4" />
                    <p>Carregando vagas...</p>
                  </div>
                ) : jobs.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    {hasActiveFilters ? (
                      <>
                        <p>Nenhuma vaga encontrada com os filtros aplicados.</p>
                        <p className="text-sm">Tente ajustar os filtros ou limpar a busca.</p>
                      </>
                    ) : (
                      <>
                        <p>Nenhuma vaga cadastrada ainda.</p>
                        <p className="text-sm">Use o web scraping ou crie uma vaga manualmente.</p>
                      </>
                    )}
                  </div>
                ) : (
                  <div className="space-y-4">
                    {jobs.map((job) => (
                      <AdminJobCard
                        key={job.id}
                        job={job}
                        onView={setViewingJob}
                        onEdit={setEditingJob}
                        onDelete={handleDeleteJob}
                        onToggleStatus={handleToggleJobStatus}
                      />
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Pagination */}
            {pagination.total > 0 && (
              <AdminJobPagination
                pagination={pagination}
                onPageChange={changePage}
                onPageSizeChange={changePageSize}
                isLoading={isLoadingJobs}
              />
            )}
          </TabsContent>
        </Tabs>
      </main>

      {/* View Job Dialog */}
      {viewingJob && (
        <JobViewDialog
          job={viewingJob}
          isOpen={!!viewingJob}
          onClose={() => setViewingJob(null)}
          onToggleStatus={handleToggleJobStatus}
        />
      )}

      {/* Edit Job Dialog */}
      {editingJob && (
        <JobEditDialog
          job={editingJob}
          isOpen={!!editingJob}
          onClose={() => setEditingJob(null)}
          onSave={(updatedData) => handleEditJob(editingJob.id, updatedData)}
        />
      )}

      {/* Delete Old Logs Dialog */}
      <DeleteOldLogsDialog
        isOpen={showDeleteLogsDialog}
        onClose={() => setShowDeleteLogsDialog(false)}
        onConfirm={deleteOldLogs}
        isLoading={isLoadingLogs}
      />
    </div>
  );
};

export default AdminPanel;