import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { 
  Collapsible, 
  CollapsibleContent, 
  CollapsibleTrigger 
} from '@/components/ui/collapsible';
import { 
  CheckCircle, 
  XCircle, 
  AlertTriangle, 
  RefreshCw, 
  ChevronDown, 
  ChevronRight,
  Clock,
  Trash2,
  Eye
} from 'lucide-react';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { ScrapingLog, SCRAPING_LOG_STATUS_CONFIG } from '@/types/scraping-log';
import { cn } from '@/lib/utils';

interface ScrapingLogTableProps {
  logs: ScrapingLog[];
  isLoading: boolean;
  onDeleteLog: (logId: string) => void;
}

const StatusIcon = ({ status }: { status: ScrapingLog['status'] }) => {
  const iconProps = { className: "h-4 w-4" };
  
  switch (status) {
    case 'running':
      return <RefreshCw {...iconProps} className="h-4 w-4 animate-spin" />;
    case 'success':
      return <CheckCircle {...iconProps} />;
    case 'error':
      return <XCircle {...iconProps} />;
    case 'partial':
      return <AlertTriangle {...iconProps} />;
    default:
      return <Clock {...iconProps} />;
  }
};

const StatusBadge = ({ status }: { status: ScrapingLog['status'] }) => {
  const config = SCRAPING_LOG_STATUS_CONFIG[status];
  
  return (
    <Badge 
      variant="outline" 
      className={cn(config.bgColor, config.color, "border")}
    >
      <StatusIcon status={status} />
      <span className="ml-1">{config.label}</span>
    </Badge>
  );
};

const formatDuration = (seconds: number | null): string => {
  if (!seconds) return '-';
  
  if (seconds < 60) {
    return `${seconds}s`;
  } else if (seconds < 3600) {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}m ${remainingSeconds}s`;
  } else {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    return `${hours}h ${minutes}m`;
  }
};

const LogDetailsRow = ({ log }: { log: ScrapingLog }) => {
  return (
    <div className="p-4 bg-muted/30 rounded-lg space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div>
          <h4 className="font-medium text-sm text-muted-foreground mb-1">Métricas de Processamento</h4>
          <div className="space-y-1 text-sm">
            <div>Total encontradas: <span className="font-medium">{log.totalJobsFound}</span></div>
            <div>Importadas: <span className="font-medium text-green-600">{log.jobsImported}</span></div>
            <div>Atualizadas: <span className="font-medium text-blue-600">{log.jobsUpdated}</span></div>
            <div>Desativadas: <span className="font-medium text-orange-600">{log.jobsDeactivated}</span></div>
            {log.jobsFailed > 0 && (
              <div>Falharam: <span className="font-medium text-red-600">{log.jobsFailed}</span></div>
            )}
          </div>
        </div>

        <div>
          <h4 className="font-medium text-sm text-muted-foreground mb-1">Timing</h4>
          <div className="space-y-1 text-sm">
            <div>Início: <span className="font-medium">{format(log.startedAt, 'dd/MM/yyyy HH:mm:ss', { locale: ptBR })}</span></div>
            {log.finishedAt && (
              <div>Fim: <span className="font-medium">{format(log.finishedAt, 'dd/MM/yyyy HH:mm:ss', { locale: ptBR })}</span></div>
            )}
            <div>Duração: <span className="font-medium">{formatDuration(log.durationSeconds)}</span></div>
          </div>
        </div>

        {log.errorMessage && (
          <div className="md:col-span-2">
            <h4 className="font-medium text-sm text-muted-foreground mb-1">Erro</h4>
            <div className="text-sm text-red-600 bg-red-50 p-2 rounded border">
              {log.errorMessage}
            </div>
          </div>
        )}

        {log.metadata && (
          <div className="md:col-span-2">
            <h4 className="font-medium text-sm text-muted-foreground mb-1">Metadados</h4>
            <pre className="text-xs bg-gray-50 p-2 rounded border overflow-x-auto">
              {JSON.stringify(log.metadata, null, 2)}
            </pre>
          </div>
        )}
      </div>
    </div>
  );
};

export function ScrapingLogTable({ logs, isLoading, onDeleteLog }: ScrapingLogTableProps) {
  const [expandedRows, setExpandedRows] = useState<Set<string>>(new Set());

  const toggleRowExpansion = (logId: string) => {
    const newExpanded = new Set(expandedRows);
    if (newExpanded.has(logId)) {
      newExpanded.delete(logId);
    } else {
      newExpanded.add(logId);
    }
    setExpandedRows(newExpanded);
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="p-8">
          <div className="flex items-center justify-center">
            <RefreshCw className="h-6 w-6 animate-spin mr-2" />
            <span>Carregando logs...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (logs.length === 0) {
    return (
      <Card>
        <CardContent className="p-8">
          <div className="text-center text-muted-foreground">
            <Eye className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>Nenhum log de execução encontrado</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Histórico de Execuções ({logs.length})</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-2">
          {logs.map((log) => {
            const isExpanded = expandedRows.has(log.id);
            
            return (
              <Collapsible key={log.id} open={isExpanded} onOpenChange={() => toggleRowExpansion(log.id)}>
                <div className="border rounded-lg">
                  <CollapsibleTrigger asChild>
                    <div className="flex items-center justify-between p-4 hover:bg-muted/50 cursor-pointer">
                      <div className="flex items-center gap-4 flex-1">
                        <div className="flex items-center gap-2">
                          {isExpanded ? (
                            <ChevronDown className="h-4 w-4" />
                          ) : (
                            <ChevronRight className="h-4 w-4" />
                          )}
                          <StatusBadge status={log.status} />
                        </div>
                        
                        <div className="flex-1 grid grid-cols-1 md:grid-cols-4 gap-4 text-sm">
                          <div>
                            <div className="font-medium">{log.source}</div>
                            <div className="text-muted-foreground">
                              {format(log.startedAt, 'dd/MM/yyyy HH:mm', { locale: ptBR })}
                            </div>
                          </div>
                          
                          <div>
                            <div className="font-medium">{formatDuration(log.durationSeconds)}</div>
                            <div className="text-muted-foreground">Duração</div>
                          </div>
                          
                          <div>
                            <div className="font-medium">{log.totalJobsFound}</div>
                            <div className="text-muted-foreground">Vagas encontradas</div>
                          </div>
                          
                          <div className="flex items-center gap-1">
                            <span className="text-green-600 font-medium">{log.jobsImported}</span>
                            <span className="text-muted-foreground">/</span>
                            <span className="text-blue-600 font-medium">{log.jobsUpdated}</span>
                            <span className="text-muted-foreground">/</span>
                            <span className="text-orange-600 font-medium">{log.jobsDeactivated}</span>
                            {log.jobsFailed > 0 && (
                              <>
                                <span className="text-muted-foreground">/</span>
                                <span className="text-red-600 font-medium">{log.jobsFailed}</span>
                              </>
                            )}
                          </div>
                        </div>
                      </div>
                      
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          onDeleteLog(log.id);
                        }}
                        className="text-red-600 hover:text-red-700 hover:bg-red-50"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </CollapsibleTrigger>
                  
                  <CollapsibleContent>
                    <div className="border-t">
                      <LogDetailsRow log={log} />
                    </div>
                  </CollapsibleContent>
                </div>
              </Collapsible>
            );
          })}
        </div>
      </CardContent>
    </Card>
  );
}
