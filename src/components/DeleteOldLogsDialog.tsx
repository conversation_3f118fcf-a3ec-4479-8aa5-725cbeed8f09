import React, { useState } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { AlertTriangle, Trash2 } from 'lucide-react';

interface DeleteOldLogsDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (daysOld: number) => Promise<void>;
  isLoading?: boolean;
}

const DELETE_OPTIONS = [
  { value: 7, label: '7 dias', description: 'Logs mais antigos que 1 semana' },
  { value: 30, label: '30 dias', description: 'Logs mais antigos que 1 mês' },
  { value: 60, label: '60 dias', description: 'Logs mais antigos que 2 meses' },
  { value: 90, label: '90 dias', description: 'Logs mais antigos que 3 meses' },
  { value: 180, label: '180 dias', description: 'Logs mais antigos que 6 meses' },
  { value: 365, label: '365 dias', description: 'Logs mais antigos que 1 ano' },
];

export function DeleteOldLogsDialog({
  isOpen,
  onClose,
  onConfirm,
  isLoading = false,
}: DeleteOldLogsDialogProps) {
  const [selectedDays, setSelectedDays] = useState<number>(30);

  const handleConfirm = async () => {
    await onConfirm(selectedDays);
    onClose();
  };

  const selectedOption = DELETE_OPTIONS.find(option => option.value === selectedDays);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Trash2 className="h-5 w-5 text-red-600" />
            Limpar Logs Antigos
          </DialogTitle>
          <DialogDescription>
            Esta ação removerá permanentemente os logs de execução antigos do banco de dados.
            Esta operação não pode ser desfeita.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 py-4">
          <div className="space-y-2">
            <Label htmlFor="days-select">Remover logs mais antigos que:</Label>
            <Select
              value={selectedDays.toString()}
              onValueChange={(value) => setSelectedDays(Number(value))}
            >
              <SelectTrigger id="days-select">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {DELETE_OPTIONS.map((option) => (
                  <SelectItem key={option.value} value={option.value.toString()}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {selectedOption && (
              <p className="text-sm text-muted-foreground">
                {selectedOption.description}
              </p>
            )}
          </div>

          <div className="flex items-start gap-3 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
            <AlertTriangle className="h-5 w-5 text-yellow-600 mt-0.5 flex-shrink-0" />
            <div className="text-sm">
              <p className="font-medium text-yellow-800 mb-1">Atenção</p>
              <p className="text-yellow-700">
                Esta operação é irreversível. Os logs removidos não poderão ser recuperados.
                Certifique-se de que não precisa mais dessas informações antes de continuar.
              </p>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={onClose}
            disabled={isLoading}
          >
            Cancelar
          </Button>
          <Button
            variant="destructive"
            onClick={handleConfirm}
            disabled={isLoading}
            className="bg-red-600 hover:bg-red-700"
          >
            {isLoading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Removendo...
              </>
            ) : (
              <>
                <Trash2 className="h-4 w-4 mr-2" />
                Confirmar Remoção
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
