import React from 'react';
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Activity, 
  CheckCircle, 
  XCircle, 
  Clock, 
  TrendingUp,
  Calendar
} from 'lucide-react';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { ScrapingLogMetrics } from '@/types/scraping-log';

interface ScrapingLogMetricsProps {
  metrics: ScrapingLogMetrics | null;
  isLoading: boolean;
}

const formatDuration = (seconds: number | null): string => {
  if (!seconds) return '-';
  
  if (seconds < 60) {
    return `${seconds}s`;
  } else if (seconds < 3600) {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}m ${remainingSeconds}s`;
  } else {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    return `${hours}h ${minutes}m`;
  }
};

const MetricCard = ({ 
  title, 
  value, 
  icon: Icon, 
  description, 
  color = "default" 
}: {
  title: string;
  value: string | number;
  icon: React.ElementType;
  description?: string;
  color?: "default" | "success" | "error" | "warning";
}) => {
  const colorClasses = {
    default: "text-blue-600 bg-blue-50 border-blue-200",
    success: "text-green-600 bg-green-50 border-green-200",
    error: "text-red-600 bg-red-50 border-red-200",
    warning: "text-orange-600 bg-orange-50 border-orange-200",
  };

  return (
    <Card>
      <CardContent className="p-6">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-muted-foreground">{title}</p>
            <p className="text-2xl font-bold">{value}</p>
            {description && (
              <p className="text-xs text-muted-foreground mt-1">{description}</p>
            )}
          </div>
          <div className={`p-2 rounded-lg border ${colorClasses[color]}`}>
            <Icon className="h-6 w-6" />
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export function ScrapingLogMetrics({ metrics, isLoading }: ScrapingLogMetricsProps) {
  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4">
        {Array.from({ length: 6 }).map((_, index) => (
          <Card key={index}>
            <CardContent className="p-6">
              <div className="animate-pulse">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-8 bg-gray-200 rounded w-1/2 mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-full"></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (!metrics) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center text-muted-foreground">
            <Activity className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>Nenhuma métrica disponível</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const successRate = metrics.totalExecutions > 0 
    ? ((metrics.successfulExecutions / metrics.totalExecutions) * 100).toFixed(1)
    : '0';

  const failureRate = metrics.totalExecutions > 0 
    ? ((metrics.failedExecutions / metrics.totalExecutions) * 100).toFixed(1)
    : '0';

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4">
        <MetricCard
          title="Total de Execuções"
          value={metrics.totalExecutions}
          icon={Activity}
          description="Todas as execuções registradas"
          color="default"
        />

        <MetricCard
          title="Execuções com Sucesso"
          value={metrics.successfulExecutions}
          icon={CheckCircle}
          description={`${successRate}% do total`}
          color="success"
        />

        <MetricCard
          title="Execuções com Falha"
          value={metrics.failedExecutions}
          icon={XCircle}
          description={`${failureRate}% do total`}
          color="error"
        />

        <MetricCard
          title="Duração Média"
          value={formatDuration(metrics.averageDuration)}
          icon={Clock}
          description="Tempo médio de execução"
          color="default"
        />

        <MetricCard
          title="Vagas Processadas"
          value={metrics.totalJobsProcessed.toLocaleString('pt-BR')}
          icon={TrendingUp}
          description="Total de vagas processadas"
          color="default"
        />

        <MetricCard
          title="Última Execução"
          value={metrics.lastExecution ? format(metrics.lastExecution, 'dd/MM', { locale: ptBR }) : '-'}
          icon={Calendar}
          description={metrics.lastExecution ? format(metrics.lastExecution, 'HH:mm', { locale: ptBR }) : 'Nenhuma execução'}
          color="default"
        />
      </div>

      {/* Success Rate Indicator */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Taxa de Sucesso</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Execuções bem-sucedidas</span>
              <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                {successRate}%
              </Badge>
            </div>
            
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className="bg-green-600 h-2 rounded-full transition-all duration-300" 
                style={{ width: `${successRate}%` }}
              ></div>
            </div>
            
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-green-600 rounded-full"></div>
                <span>Sucesso: {metrics.successfulExecutions}</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-red-600 rounded-full"></div>
                <span>Falha: {metrics.failedExecutions}</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
