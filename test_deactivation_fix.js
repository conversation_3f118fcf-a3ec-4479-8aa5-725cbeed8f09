// Script para testar se a correção do timestamp de desativação está funcionando
// Execute este script após aplicar a migração SQL

const { createClient } = require('@supabase/supabase-js');
const supabase = createClient(
  'https://iilmclwbargzfikbksrt.supabase.co', 
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImlpbG1jbHdiYXJnemZpa2Jrc3J0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTMxODA0ODgsImV4cCI6MjA2ODc1NjQ4OH0.lHviQFehcp2y3pE7-UVAncoob67yqco1P4v47ZJ-780'
);

async function testDeactivationTimestampFix() {
  console.log('🧪 TESTE: Correção do Timestamp de Desativação');
  console.log('=' .repeat(50));
  
  try {
    // 1. Verificar se a migração foi aplicada
    console.log('1️⃣ Verificando se a migração foi aplicada...');
    const { data: testField, error: fieldError } = await supabase
      .from('jobs')
      .select('id, deactivated_at')
      .limit(1);
    
    if (fieldError) {
      console.log('❌ FALHA: Campo deactivated_at não existe no banco!');
      console.log('📋 Aplique primeiro a migração usando o arquivo apply_deactivated_at_migration.sql');
      return;
    }
    console.log('✅ Campo deactivated_at existe no banco');
    
    // 2. Criar uma vaga de teste
    console.log('\n2️⃣ Criando vaga de teste...');
    const testJobData = {
      external_id: `test-timestamp-fix-${Date.now()}`,
      title: 'Teste - Correção Timestamp Desativação',
      company: 'Empresa Teste',
      location: 'Franca, SP',
      category: 'Teste',
      description: 'Vaga criada para testar a correção do timestamp de desativação.',
      is_active: true
    };

    const { data: createdJob, error: createError } = await supabase
      .from('jobs')
      .insert(testJobData)
      .select()
      .single();

    if (createError) {
      console.log('❌ Erro ao criar vaga de teste:', createError.message);
      return;
    }
    console.log('✅ Vaga de teste criada:', createdJob.id);

    // 3. Desativar a vaga
    console.log('\n3️⃣ Desativando a vaga...');
    const deactivationTime = new Date();
    
    const { error: deactivateError } = await supabase
      .from('jobs')
      .update({ is_active: false })
      .eq('id', createdJob.id);

    if (deactivateError) {
      console.log('❌ Erro ao desativar vaga:', deactivateError.message);
      return;
    }
    console.log('✅ Vaga desativada');

    // 4. Aguardar um pouco para o trigger processar
    await new Promise(resolve => setTimeout(resolve, 1000));

    // 5. Verificar se o timestamp foi definido corretamente
    console.log('\n4️⃣ Verificando timestamp de desativação...');
    const { data: deactivatedJob, error: fetchError } = await supabase
      .from('jobs')
      .select('id, title, is_active, deactivated_at, created_at')
      .eq('id', createdJob.id)
      .single();

    if (fetchError) {
      console.log('❌ Erro ao buscar vaga desativada:', fetchError.message);
      return;
    }

    console.log('📊 Estado da vaga após desativação:');
    console.log('   ID:', deactivatedJob.id);
    console.log('   Título:', deactivatedJob.title);
    console.log('   Ativa:', deactivatedJob.is_active);
    console.log('   Criada em:', deactivatedJob.created_at);
    console.log('   Desativada em:', deactivatedJob.deactivated_at);

    // 6. Validar se o timestamp está correto
    if (!deactivatedJob.deactivated_at) {
      console.log('❌ FALHA: deactivated_at não foi definido!');
      console.log('🔧 Verifique se o trigger foi criado corretamente');
      return;
    }

    const deactivatedAt = new Date(deactivatedJob.deactivated_at);
    const timeDiff = Math.abs(deactivatedAt.getTime() - deactivationTime.getTime());
    
    if (timeDiff > 5000) { // Mais de 5 segundos de diferença
      console.log('⚠️ AVISO: Timestamp pode estar incorreto');
      console.log('   Esperado próximo a:', deactivationTime.toISOString());
      console.log('   Obtido:', deactivatedAt.toISOString());
    } else {
      console.log('✅ Timestamp de desativação está correto!');
    }

    // 7. Testar múltiplas consultas para verificar se o timestamp permanece fixo
    console.log('\n5️⃣ Testando se o timestamp permanece fixo...');
    
    const timestamps = [];
    for (let i = 0; i < 3; i++) {
      await new Promise(resolve => setTimeout(resolve, 500));
      
      const { data: checkJob, error: checkError } = await supabase
        .from('jobs')
        .select('deactivated_at')
        .eq('id', createdJob.id)
        .single();
      
      if (!checkError && checkJob) {
        timestamps.push(checkJob.deactivated_at);
        console.log(`   Consulta ${i + 1}: ${checkJob.deactivated_at}`);
      }
    }

    // Verificar se todos os timestamps são iguais
    const allSame = timestamps.every(ts => ts === timestamps[0]);
    if (allSame) {
      console.log('✅ SUCESSO: Timestamp permanece fixo entre consultas!');
    } else {
      console.log('❌ FALHA: Timestamp está mudando entre consultas!');
    }

    // 8. Testar reativação
    console.log('\n6️⃣ Testando reativação da vaga...');
    const { error: reactivateError } = await supabase
      .from('jobs')
      .update({ is_active: true })
      .eq('id', createdJob.id);

    if (reactivateError) {
      console.log('❌ Erro ao reativar vaga:', reactivateError.message);
    } else {
      await new Promise(resolve => setTimeout(resolve, 500));
      
      const { data: reactivatedJob, error: reactivatedError } = await supabase
        .from('jobs')
        .select('is_active, deactivated_at')
        .eq('id', createdJob.id)
        .single();

      if (!reactivatedError && reactivatedJob) {
        if (reactivatedJob.is_active && reactivatedJob.deactivated_at === null) {
          console.log('✅ Reativação funcionou corretamente - deactivated_at foi limpo');
        } else {
          console.log('⚠️ Problema na reativação:');
          console.log('   is_active:', reactivatedJob.is_active);
          console.log('   deactivated_at:', reactivatedJob.deactivated_at);
        }
      }
    }

    // 9. Limpar vaga de teste
    console.log('\n7️⃣ Limpando vaga de teste...');
    const { error: deleteError } = await supabase
      .from('jobs')
      .delete()
      .eq('id', createdJob.id);

    if (deleteError) {
      console.log('⚠️ Não foi possível deletar a vaga de teste:', deleteError.message);
    } else {
      console.log('✅ Vaga de teste removida');
    }

    console.log('\n🎉 TESTE CONCLUÍDO COM SUCESSO!');
    console.log('✅ A correção do timestamp de desativação está funcionando corretamente');
    
  } catch (error) {
    console.error('💥 Erro durante o teste:', error.message);
  }
}

// Executar o teste
testDeactivationTimestampFix();
