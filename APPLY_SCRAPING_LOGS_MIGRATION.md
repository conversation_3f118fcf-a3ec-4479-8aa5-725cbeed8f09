# Instruções para Aplicar a Migração da Tabela scraping_logs

## Problema
A tabela `scraping_logs` não existe no banco de dados, sendo necessária para o sistema de logging de execuções do scraping.

## Solução - Aplicar Migração

### Via Dashboard do Supabase
1. Acesse: https://supabase.com/dashboard/project/iilmclwbargzfikbksrt/sql
2. Execute o seguinte SQL (conteúdo do arquivo `supabase/migrations/20250723120000_create_scraping_logs.sql`):

```sql
-- Create scraping_logs table for tracking web scraping execution logs
CREATE TABLE public.scraping_logs (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  
  -- Execution timing
  started_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  finished_at TIMESTAMP WITH TIME ZONE NULL,
  duration_seconds INTEGER NULL, -- Calculated duration in seconds
  
  -- Execution details
  status TEXT NOT NULL CHECK (status IN ('running', 'success', 'error', 'partial')),
  source TEXT NOT NULL DEFAULT 'HardFranca', -- Source platform/website
  
  -- Job metrics
  total_jobs_found INTEGER NOT NULL DEFAULT 0,
  jobs_imported INTEGER NOT NULL DEFAULT 0,
  jobs_updated INTEGER NOT NULL DEFAULT 0,
  jobs_deactivated INTEGER NOT NULL DEFAULT 0,
  jobs_failed INTEGER NOT NULL DEFAULT 0,
  
  -- Error details
  error_message TEXT NULL,
  error_details JSONB NULL, -- Store detailed error information
  
  -- Additional metadata
  metadata JSONB NULL, -- Store additional execution metadata
  
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Enable Row Level Security
ALTER TABLE public.scraping_logs ENABLE ROW LEVEL SECURITY;

-- Create policies for public read access (admin functionality)
CREATE POLICY "Scraping logs are viewable by everyone" ON public.scraping_logs 
FOR SELECT USING (true);

CREATE POLICY "Admins can insert scraping logs" ON public.scraping_logs 
FOR INSERT WITH CHECK (true);

CREATE POLICY "Admins can update scraping logs" ON public.scraping_logs 
FOR UPDATE USING (true);

CREATE POLICY "Admins can delete scraping logs" ON public.scraping_logs 
FOR DELETE USING (true);

-- Create function to update timestamps and calculate duration
CREATE OR REPLACE FUNCTION public.update_scraping_log_timestamps() 
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  
  -- Calculate duration if finished_at is set
  IF NEW.finished_at IS NOT NULL AND NEW.started_at IS NOT NULL THEN
    NEW.duration_seconds = EXTRACT(EPOCH FROM (NEW.finished_at - NEW.started_at))::INTEGER;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for automatic timestamp and duration updates
CREATE TRIGGER update_scraping_logs_timestamps 
BEFORE UPDATE ON public.scraping_logs 
FOR EACH ROW EXECUTE FUNCTION public.update_scraping_log_timestamps();

-- Create indexes for better performance
CREATE INDEX idx_scraping_logs_started_at ON public.scraping_logs(started_at DESC);
CREATE INDEX idx_scraping_logs_status ON public.scraping_logs(status);
CREATE INDEX idx_scraping_logs_source ON public.scraping_logs(source);
CREATE INDEX idx_scraping_logs_status_source ON public.scraping_logs(status, source);
CREATE INDEX idx_scraping_logs_created_at ON public.scraping_logs(created_at DESC);

-- Add comments for documentation
COMMENT ON TABLE public.scraping_logs IS 'Logs of web scraping execution with metrics and error tracking';
COMMENT ON COLUMN public.scraping_logs.started_at IS 'When the scraping execution started';
COMMENT ON COLUMN public.scraping_logs.finished_at IS 'When the scraping execution finished (NULL if still running)';
COMMENT ON COLUMN public.scraping_logs.duration_seconds IS 'Total execution duration in seconds (auto-calculated)';
COMMENT ON COLUMN public.scraping_logs.status IS 'Execution status: running, success, error, partial';
COMMENT ON COLUMN public.scraping_logs.source IS 'Source platform/website being scraped';
COMMENT ON COLUMN public.scraping_logs.total_jobs_found IS 'Total number of jobs found during scraping';
COMMENT ON COLUMN public.scraping_logs.jobs_imported IS 'Number of new jobs imported';
COMMENT ON COLUMN public.scraping_logs.jobs_updated IS 'Number of existing jobs updated';
COMMENT ON COLUMN public.scraping_logs.jobs_deactivated IS 'Number of jobs marked as inactive';
COMMENT ON COLUMN public.scraping_logs.jobs_failed IS 'Number of jobs that failed to process';
COMMENT ON COLUMN public.scraping_logs.error_message IS 'Main error message if execution failed';
COMMENT ON COLUMN public.scraping_logs.error_details IS 'Detailed error information in JSON format';
COMMENT ON COLUMN public.scraping_logs.metadata IS 'Additional execution metadata in JSON format';
```

### Verificação
Após executar a migração, verifique se a tabela foi criada corretamente:

```sql
-- Verificar se a tabela foi criada
SELECT table_name FROM information_schema.tables 
WHERE table_schema = 'public' AND table_name = 'scraping_logs';

-- Verificar as colunas da tabela
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'scraping_logs' AND table_schema = 'public'
ORDER BY ordinal_position;

-- Verificar os índices criados
SELECT indexname, indexdef 
FROM pg_indexes 
WHERE tablename = 'scraping_logs' AND schemaname = 'public';
```

## Após Aplicar a Migração

### Testar o Sistema
1. Acesse o painel administrativo
2. Vá para a aba "Logs de Execução"
3. Execute um scraping na aba "Web Scraping"
4. Verifique se o log da execução aparece na aba de logs
5. Teste os filtros, paginação e exportação

### Funcionalidades Disponíveis
- ✅ Dashboard com métricas de execução
- ✅ Tabela de logs com detalhes expandíveis
- ✅ Filtros por status, fonte, data e busca
- ✅ Paginação e ordenação
- ✅ Exportação em CSV e JSON
- ✅ Limpeza de logs antigos
- ✅ Notificações para execuções com falha
- ✅ Indicadores visuais de status
- ✅ Rastreamento automático de duração

## Estrutura Implementada

### Tabela scraping_logs
- **Timing**: started_at, finished_at, duration_seconds
- **Status**: running, success, error, partial
- **Métricas**: total_jobs_found, jobs_imported, jobs_updated, jobs_deactivated, jobs_failed
- **Erros**: error_message, error_details
- **Metadados**: metadata (JSON)

### Componentes React
- `ScrapingLogMetrics`: Dashboard com métricas
- `ScrapingLogFilters`: Filtros e ações
- `ScrapingLogTable`: Tabela com detalhes expandíveis
- `ScrapingLogPagination`: Paginação
- `DeleteOldLogsDialog`: Diálogo de confirmação

### Hook personalizado
- `useScrapingLogs`: Gerenciamento completo dos logs

### Integração
- Nova aba "Logs de Execução" no AdminPanel
- Logging automático na função Edge do Supabase
- Notificações para execuções com falha
