-- Migration: Add deactivated_at field to jobs table
-- Execute this SQL in the Supabase SQL Editor: https://supabase.com/dashboard/project/iilmclwbargzfikbksrt/sql

-- Step 1: Add deactivated_at field to jobs table for tracking when jobs are deactivated
ALTER TABLE public.jobs 
ADD COLUMN IF NOT EXISTS deactivated_at TIMESTAMP WITH TIME ZONE NULL;

-- Step 2: Add comment to explain the field
COMMENT ON COLUMN public.jobs.deactivated_at IS 'Timestamp when the job was deactivated (set to NULL when reactivated)';

-- Step 3: Create index for better performance when filtering by deactivation date
CREATE INDEX IF NOT EXISTS idx_jobs_deactivated_at ON public.jobs(deactivated_at);

-- Step 4: Create composite index for filtering active/inactive jobs with deactivation date
CREATE INDEX IF NOT EXISTS idx_jobs_is_active_deactivated_at ON public.jobs(is_active, deactivated_at DESC);

-- Step 5: Create function to automatically set deactivated_at when is_active changes
CREATE OR REPLACE FUNCTION public.handle_job_deactivation() 
RETURNS TRIGGER AS $$
BEGIN
  -- If job is being deactivated (is_active changed from true to false)
  IF OLD.is_active = true AND NEW.is_active = false THEN
    NEW.deactivated_at = now();
  -- If job is being reactivated (is_active changed from false to true)
  ELSIF OLD.is_active = false AND NEW.is_active = true THEN
    NEW.deactivated_at = NULL;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Step 6: Create trigger to automatically handle deactivation timestamps
DROP TRIGGER IF EXISTS trigger_job_deactivation ON public.jobs;
CREATE TRIGGER trigger_job_deactivation
  BEFORE UPDATE OF is_active ON public.jobs
  FOR EACH ROW
  EXECUTE FUNCTION public.handle_job_deactivation();

-- Step 7: Update existing inactive jobs to have a deactivated_at timestamp
-- This sets a reasonable timestamp for jobs that are already inactive
UPDATE public.jobs 
SET deactivated_at = updated_at 
WHERE is_active = false AND deactivated_at IS NULL;

-- Verification queries (run these to confirm the migration worked):
-- SELECT column_name, data_type FROM information_schema.columns WHERE table_name = 'jobs' AND column_name = 'deactivated_at';
-- SELECT COUNT(*) as total_jobs, COUNT(deactivated_at) as jobs_with_deactivated_at FROM public.jobs WHERE is_active = false;
