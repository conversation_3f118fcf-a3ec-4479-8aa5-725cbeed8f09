-- Create scraping_logs table for tracking web scraping execution logs
CREATE TABLE public.scraping_logs (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  
  -- Execution timing
  started_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  finished_at TIMESTAMP WITH TIME ZONE NULL,
  duration_seconds INTEGER NULL, -- Calculated duration in seconds
  
  -- Execution details
  status TEXT NOT NULL CHECK (status IN ('running', 'success', 'error', 'partial')),
  source TEXT NOT NULL DEFAULT 'HardFranca', -- Source platform/website
  
  -- Job metrics
  total_jobs_found INTEGER NOT NULL DEFAULT 0,
  jobs_imported INTEGER NOT NULL DEFAULT 0,
  jobs_updated INTEGER NOT NULL DEFAULT 0,
  jobs_deactivated INTEGER NOT NULL DEFAULT 0,
  jobs_failed INTEGER NOT NULL DEFAULT 0,
  
  -- Error details
  error_message TEXT NULL,
  error_details JSONB NULL, -- Store detailed error information
  
  -- Additional metadata
  metadata JSONB NULL, -- Store additional execution metadata
  
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Enable Row Level Security
ALTER TABLE public.scraping_logs ENABLE ROW LEVEL SECURITY;

-- Create policies for public read access (admin functionality)
CREATE POLICY "Scraping logs are viewable by everyone" ON public.scraping_logs 
FOR SELECT USING (true);

CREATE POLICY "Admins can insert scraping logs" ON public.scraping_logs 
FOR INSERT WITH CHECK (true);

CREATE POLICY "Admins can update scraping logs" ON public.scraping_logs 
FOR UPDATE USING (true);

CREATE POLICY "Admins can delete scraping logs" ON public.scraping_logs 
FOR DELETE USING (true);

-- Create function to update timestamps and calculate duration
CREATE OR REPLACE FUNCTION public.update_scraping_log_timestamps() 
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  
  -- Calculate duration if finished_at is set
  IF NEW.finished_at IS NOT NULL AND NEW.started_at IS NOT NULL THEN
    NEW.duration_seconds = EXTRACT(EPOCH FROM (NEW.finished_at - NEW.started_at))::INTEGER;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for automatic timestamp and duration updates
CREATE TRIGGER update_scraping_logs_timestamps 
BEFORE UPDATE ON public.scraping_logs 
FOR EACH ROW EXECUTE FUNCTION public.update_scraping_log_timestamps();

-- Create indexes for better performance
CREATE INDEX idx_scraping_logs_started_at ON public.scraping_logs(started_at DESC);
CREATE INDEX idx_scraping_logs_status ON public.scraping_logs(status);
CREATE INDEX idx_scraping_logs_source ON public.scraping_logs(source);
CREATE INDEX idx_scraping_logs_status_source ON public.scraping_logs(status, source);
CREATE INDEX idx_scraping_logs_created_at ON public.scraping_logs(created_at DESC);

-- Add comments for documentation
COMMENT ON TABLE public.scraping_logs IS 'Logs of web scraping execution with metrics and error tracking';
COMMENT ON COLUMN public.scraping_logs.started_at IS 'When the scraping execution started';
COMMENT ON COLUMN public.scraping_logs.finished_at IS 'When the scraping execution finished (NULL if still running)';
COMMENT ON COLUMN public.scraping_logs.duration_seconds IS 'Total execution duration in seconds (auto-calculated)';
COMMENT ON COLUMN public.scraping_logs.status IS 'Execution status: running, success, error, partial';
COMMENT ON COLUMN public.scraping_logs.source IS 'Source platform/website being scraped';
COMMENT ON COLUMN public.scraping_logs.total_jobs_found IS 'Total number of jobs found during scraping';
COMMENT ON COLUMN public.scraping_logs.jobs_imported IS 'Number of new jobs imported';
COMMENT ON COLUMN public.scraping_logs.jobs_updated IS 'Number of existing jobs updated';
COMMENT ON COLUMN public.scraping_logs.jobs_deactivated IS 'Number of jobs marked as inactive';
COMMENT ON COLUMN public.scraping_logs.jobs_failed IS 'Number of jobs that failed to process';
COMMENT ON COLUMN public.scraping_logs.error_message IS 'Main error message if execution failed';
COMMENT ON COLUMN public.scraping_logs.error_details IS 'Detailed error information in JSON format';
COMMENT ON COLUMN public.scraping_logs.metadata IS 'Additional execution metadata in JSON format';
