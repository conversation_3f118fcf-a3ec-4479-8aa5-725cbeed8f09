import { serve } from "https://deno.land/std@0.190.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

interface ScrapedJob {
  external_id: string;
  title: string;
  description: string;
}

const handler = async (req: Request): Promise<Response> => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  // Initialize Supabase client
  const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
  const supabaseKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
  const supabase = createClient(supabaseUrl, supabaseKey);

  // Create initial log entry
  const startTime = new Date();
  let logId: string | null = null;

  try {
    console.log('Starting HardFranca scraping...');

    // Create scraping log entry
    console.log('Attempting to create scraping log...');
    const { data: logData, error: logError } = await supabase
      .from('scraping_logs')
      .insert({
        status: 'running',
        source: 'HardFranca',
        started_at: startTime.toISOString(),
      })
      .select('id')
      .single();

    if (logError) {
      console.error('Error creating scraping log:', logError);
      console.error('Log error details:', JSON.stringify(logError, null, 2));
    } else {
      logId = logData?.id || null;
      console.log('Successfully created scraping log with ID:', logId);
      console.log('Log data:', JSON.stringify(logData, null, 2));
    }

    // Fetch the HardFranca page
    const response = await fetch('https://hardfranca.com.br/anuncios.php');
    
    if (!response.ok) {
      throw new Error(`Failed to fetch HardFranca page: ${response.status}`);
    }

    const html = await response.text();
    console.log('Successfully fetched HardFranca page');

    // Parse HTML and extract job data
    const jobs = extractJobsFromHtml(html);
    console.log(`Extracted ${jobs.length} jobs from HTML`);

    if (jobs.length === 0) {
      // Update log for no jobs found
      if (logId) {
        await supabase
          .from('scraping_logs')
          .update({
            status: 'success',
            finished_at: new Date().toISOString(),
            total_jobs_found: 0,
            jobs_imported: 0,
            jobs_updated: 0,
            jobs_deactivated: 0,
            jobs_failed: 0,
          })
          .eq('id', logId);
      }

      return new Response(
        JSON.stringify({
          success: true,
          message: 'No jobs found on the page',
          jobs_processed: 0
        }),
        {
          status: 200,
          headers: { "Content-Type": "application/json", ...corsHeaders },
        }
      );
    }

    // Insert jobs into database
    let newJobsCount = 0;
    let updatedJobsCount = 0;
    let errorCount = 0;

    for (const job of jobs) {
      try {
        // First check if job already exists
        const { data: existingJob } = await supabase
          .from('jobs')
          .select('id, is_active')
          .eq('external_id', job.external_id)
          .single();

        if (existingJob) {
          // Job exists, update it and mark as active
          const { error } = await supabase
            .from('jobs')
            .update({
              title: job.title,
              description: job.description,
              location: 'Franca, SP',
              category: 'Geral',
              company: 'HardFranca',
              is_active: true
            })
            .eq('external_id', job.external_id);

          if (error) {
            console.error(`Error updating job ${job.external_id}:`, error);
            errorCount++;
          } else {
            console.log(`Successfully updated job ${job.external_id}`);
            updatedJobsCount++;
          }
        } else {
          // Job doesn't exist, insert new one
          const { error } = await supabase
            .from('jobs')
            .insert({
              external_id: job.external_id,
              title: job.title,
              description: job.description,
              location: 'Franca, SP',
              category: 'Geral',
              company: 'HardFranca',
              is_active: true
            });

          if (error) {
            console.error(`Error inserting job ${job.external_id}:`, error);
            errorCount++;
          } else {
            console.log(`Successfully inserted new job ${job.external_id}`);
            newJobsCount++;
          }
        }
      } catch (error) {
        console.error(`Exception processing job ${job.external_id}:`, error);
        errorCount++;
      }
    }

    // Mark jobs as inactive if they no longer exist on the website
    let deactivatedJobsCount = 0;
    try {
      const scrapedExternalIds = jobs.map(job => job.external_id);

      // Get all active HardFranca jobs from database
      const { data: existingJobs, error: fetchError } = await supabase
        .from('jobs')
        .select('external_id')
        .eq('company', 'HardFranca')
        .eq('is_active', true);

      if (fetchError) {
        console.error('Error fetching existing jobs:', fetchError);
      } else if (existingJobs) {
        // Find jobs that exist in database but not in scraped data
        const jobsToDeactivate = existingJobs
          .filter(existingJob => !scrapedExternalIds.includes(existingJob.external_id))
          .map(job => job.external_id);

        if (jobsToDeactivate.length > 0) {
          console.log(`Deactivating ${jobsToDeactivate.length} jobs that no longer exist on website:`, jobsToDeactivate);

          const { error: deactivateError } = await supabase
            .from('jobs')
            .update({ is_active: false })
            .in('external_id', jobsToDeactivate)
            .eq('company', 'HardFranca');

          if (deactivateError) {
            console.error('Error deactivating jobs:', deactivateError);
          } else {
            deactivatedJobsCount = jobsToDeactivate.length;
            console.log(`Successfully deactivated ${deactivatedJobsCount} jobs`);
          }
        }
      }
    } catch (error) {
      console.error('Exception during job deactivation:', error);
    }

    console.log(`Scraping completed: ${newJobsCount} new jobs, ${updatedJobsCount} updated jobs, ${deactivatedJobsCount} deactivated jobs, ${errorCount} errors`);

    // Update log with final results
    const finishTime = new Date();
    const finalStatus = errorCount === jobs.length ? 'error' :
                       errorCount > 0 ? 'partial' : 'success';

    if (logId) {
      console.log('Updating scraping log with final results...');
      const updateData = {
        status: finalStatus,
        finished_at: finishTime.toISOString(),
        total_jobs_found: jobs.length,
        jobs_imported: newJobsCount,
        jobs_updated: updatedJobsCount,
        jobs_deactivated: deactivatedJobsCount,
        jobs_failed: errorCount,
        metadata: {
          execution_summary: `${newJobsCount} imported, ${updatedJobsCount} updated, ${deactivatedJobsCount} deactivated, ${errorCount} failed`,
          processing_details: {
            total_found: jobs.length,
            success_rate: jobs.length > 0 ? ((jobs.length - errorCount) / jobs.length * 100).toFixed(2) + '%' : '0%'
          }
        }
      };

      console.log('Update data:', JSON.stringify(updateData, null, 2));

      const { error: updateError } = await supabase
        .from('scraping_logs')
        .update(updateData)
        .eq('id', logId);

      if (updateError) {
        console.error('Error updating scraping log:', updateError);
      } else {
        console.log('Successfully updated scraping log');
      }
    }

    return new Response(
      JSON.stringify({
        success: true,
        message: `Scraping completed successfully`,
        jobs_imported: newJobsCount,
        jobs_updated: updatedJobsCount,
        jobs_deactivated: deactivatedJobsCount,
        jobs_failed: errorCount,
        total_found: jobs.length,
        log_id: logId
      }),
      {
        status: 200,
        headers: { "Content-Type": "application/json", ...corsHeaders },
      }
    );

  } catch (error: any) {
    console.error('Error in scrape-hardfranca function:', error);

    // Update log with error information
    if (logId) {
      try {
        await supabase
          .from('scraping_logs')
          .update({
            status: 'error',
            finished_at: new Date().toISOString(),
            error_message: error.message,
            error_details: {
              stack: error.stack,
              name: error.name,
              timestamp: new Date().toISOString()
            }
          })
          .eq('id', logId);
      } catch (logUpdateError) {
        console.error('Error updating scraping log:', logUpdateError);
      }
    }

    return new Response(
      JSON.stringify({
        success: false,
        error: error.message,
        log_id: logId
      }),
      {
        status: 500,
        headers: { "Content-Type": "application/json", ...corsHeaders },
      }
    );
  }
};

function extractJobsFromHtml(html: string): ScrapedJob[] {
  const jobs: ScrapedJob[] = [];
  
  try {
    console.log('Starting HTML parsing...');
    
    // Find all job containers with pattern anuncio_completo_dest{CODE}
    // Using a more flexible regex pattern
    const jobRegex = /<div[^>]*id="anuncio_completo_dest(\d+)"[^>]*>([\s\S]*?)(?=<div[^>]*id="anuncio_completo_dest\d+"|<\/div>\s*<\/div>\s*$)/g;
    
    let match;
    let jobCount = 0;
    
    while ((match = jobRegex.exec(html)) !== null) {
      const externalId = match[1];
      const jobHtml = match[2];
      jobCount++;
      
      console.log(`Processing job ${jobCount} with ID: ${externalId}`);
      
      // Extract title from div with class "titulo-anuncio"
      const titleRegex = /<div[^>]*class="titulo-anuncio"[^>]*>(.*?)<\/div>/s;
      const titleMatch = jobHtml.match(titleRegex);
      
      if (!titleMatch) {
        console.log(`No title found for job ${externalId}`);
        continue;
      }
      
      const title = cleanHtmlText(titleMatch[1]);
      console.log(`Found title: ${title}`);
      
      // Try multiple patterns for description
      let description = '';
      
      // Pattern 1: div with style containing "line-height: 20px" after title
      const descPattern1 = /<div[^>]*class="titulo-anuncio"[^>]*>.*?<\/div>\s*<div[^>]*style="[^"]*line-height:\s*20px[^"]*"[^>]*>(.*?)<\/div>/s;
      let descMatch = jobHtml.match(descPattern1);
      
      if (descMatch) {
        description = cleanHtmlText(descMatch[1]);
        console.log(`Found description with pattern 1: ${description.substring(0, 100)}...`);
      } else {
        // Pattern 2: any div immediately after title div
        const descPattern2 = /<div[^>]*class="titulo-anuncio"[^>]*>.*?<\/div>\s*<div[^>]*>(.*?)<\/div>/s;
        descMatch = jobHtml.match(descPattern2);
        
        if (descMatch) {
          description = cleanHtmlText(descMatch[1]);
          console.log(`Found description with pattern 2: ${description.substring(0, 100)}...`);
        } else {
          // Pattern 3: Look for any text content after the title
          const afterTitle = jobHtml.substring(jobHtml.indexOf('</div>') + 6);
          const textMatch = afterTitle.match(/>\s*([^<]+)/);
          if (textMatch) {
            description = cleanHtmlText(textMatch[1]);
            console.log(`Found description with pattern 3: ${description.substring(0, 100)}...`);
          }
        }
      }
      
      if (title && description && description.length > 10) {
        jobs.push({
          external_id: externalId,
          title: title,
          description: description
        });
        
        console.log(`Successfully extracted job: ${externalId} - ${title.substring(0, 50)}...`);
      } else {
        console.log(`Incomplete data for job ${externalId}: title=${!!title}, description=${!!description} (length: ${description.length})`);
        console.log(`Title: "${title}"`);
        console.log(`Description: "${description}"`);
      }
    }
    
    console.log(`Total jobs processed: ${jobCount}, Successfully extracted: ${jobs.length}`);
    
    // If no jobs found with the main pattern, try alternative approach
    if (jobs.length === 0) {
      console.log('No jobs found with main pattern, trying alternative approach...');
      
      // Look for any div with "titulo-anuncio" class
      const titleElements = html.match(/<div[^>]*class="titulo-anuncio"[^>]*>.*?<\/div>/g);
      console.log(`Found ${titleElements?.length || 0} title elements`);
      
      if (titleElements) {
        titleElements.forEach((titleElement, index) => {
          const title = cleanHtmlText(titleElement.replace(/<[^>]*>/g, ''));
          console.log(`Alternative extraction ${index + 1}: ${title}`);
        });
      }
    }
    
  } catch (error) {
    console.error('Error parsing HTML:', error);
  }
  
  return jobs;
}

function cleanHtmlText(text: string): string {
  return text
    .replace(/<[^>]*>/g, '') // Remove HTML tags
    .replace(/&nbsp;/g, ' ') // Replace &nbsp; with space
    .replace(/&amp;/g, '&') // Replace &amp; with &
    .replace(/&lt;/g, '<') // Replace &lt; with <
    .replace(/&gt;/g, '>') // Replace &gt; with >
    .replace(/&quot;/g, '"') // Replace &quot; with "
    .replace(/&#39;/g, "'") // Replace &#39; with '
    .replace(/\s+/g, ' ') // Replace multiple spaces with single space
    .trim(); // Remove leading/trailing whitespace
}

serve(handler);